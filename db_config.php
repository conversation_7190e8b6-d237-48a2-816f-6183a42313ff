<?php
// db_config.php

// エラーを画面に表示する（開発時のみ）
ini_set('display_errors', 1);
error_reporting(E_ALL);

// InfinityFreeから取得したあなたのデータベース情報に書き換えてください
$servername = "sql103.infinityfree.com";
$username = "if0_39169766";
$password = "wuyao19941118";
$dbname = "if0_39169766_jsq";

// データベースに接続
$conn = new mysqli($servername, $username, $password, $dbname);

// 接続エラーの確認
if ($conn->connect_error) {
  // エラーメッセージをJSON形式で返して終了
  header('Content-Type: application/json');
  die(json_encode(['success' => false, 'message' => 'Database connection failed: ' . $conn->connect_error]));
}

// 文字コードを設定
$conn->set_charset("utf8mb4");
?>