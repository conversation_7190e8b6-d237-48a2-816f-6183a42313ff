<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在线表单生成器 (数据库版)</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif; display: flex; flex-wrap: wrap; gap: 20px; padding: 20px; }
        .column { border: 1px solid #ccc; padding: 20px; border-radius: 8px; background-color: #f9f9f9; }
        .builder-col { width: 100%; order: 1; }
        .preview-col { width: calc(50% - 10px); order: 2; }
        .output-col { width: calc(50% - 10px); order: 3; }
        .db-col { width: 100%; order: 4; margin-top: 20px; }
        h2 { margin-top: 0; border-bottom: 2px solid #007bff; padding-bottom: 10px; color: #007bff; }
        .control-group { display: flex; gap: 10px; align-items: center; padding: 8px; border: 1px dashed #ddd; border-radius: 4px; margin-bottom: 5px; }
        input[type="text"], input[type="number"], textarea { width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box; }
        button { padding: 10px 15px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; transition: background-color 0.3s; }
        button:hover { background-color: #0056b3; }
        .delete-btn { background-color: #dc3545; padding: 5px 10px; min-width: 50px; }
        .delete-btn:hover { background-color: #c82333; }
        .save-btn { background-color: #28a745; }
        .save-btn:hover { background-color: #218838; }
        #generated-html { min-height: 200px; white-space: pre; overflow-x: auto; background-color: #eef; font-family: monospace; }
        #saved-forms-list ul { list-style-type: none; padding: 0; }
        #saved-forms-list li { padding: 10px; border-bottom: 1px solid #eee; cursor: pointer; }
        #saved-forms-list li:hover { background-color: #f0f0f0; }
        #status-message { margin-top: 10px; padding: 10px; border-radius: 4px; text-align: center; display: none; }
        .status-success { background-color: #d4edda; color: #155724; }
        .status-error { background-color: #f8d7da; color: #721c24; }
    </style>
</head>
<body>

    <div class="column builder-col">
        <h2>1. 设计表单</h2>
        <button id="add-input">添加输入项</button>
        <div id="input-fields"></div>
        <div>
            <label for="formula">请输入计算公式:</label>
            <textarea id="formula" rows="3" placeholder="例如: input1 * input2"></textarea>
        </div>
        <div>
            <label for="result-label">结果标签:</label>
            <input type="text" id="result-label" value="计算结果:">
        </div>
    </div>

    <div class="column preview-col">
        <h2>2. 预览</h2>
        <div id="preview-area"></div>
    </div>

    <div class="column output-col">
        <h2>3. 输出 & 保存</h2>
        <div>
            <label for="form-name-input">保存名称:</label>
            <input type="text" id="form-name-input" placeholder="我的第一个表单">
        </div>
        <button id="save-to-db" class="save-btn">保存到数据库</button>
        <textarea id="generated-html" rows="10" readonly placeholder="点击“生成HTML”或从数据库加载..."></textarea>
        <div id="status-message"></div>
    </div>

    <div class="column db-col">
        <h2>4. 已保存的表单</h2>
        <button id="list-forms-btn">刷新列表</button>
        <div id="saved-forms-list"><p>点击“刷新列表”以显示已保存的表单。</p></div>
    </div>

<script>
    // --- DOM Elements ---
    const builderInputsContainer = document.getElementById('input-fields');
    const formulaInput = document.getElementById('formula');
    const resultLabelInput = document.getElementById('result-label');
    const previewArea = document.getElementById('preview-area');
    const generatedHtmlTextarea = document.getElementById('generated-html');
    const formNameInput = document.getElementById('form-name-input');
    const statusMessageDiv = document.getElementById('status-message');
    const savedFormsListDiv = document.getElementById('saved-forms-list');
    let inputEditorCounter = 0;

    // --- Event Listeners ---
    document.getElementById('add-input').addEventListener('click', addInputField);
    document.getElementById('save-to-db').addEventListener('click', saveFormToDB);
    document.getElementById('list-forms-btn').addEventListener('click', listFormsFromDB);
    builderInputsContainer.addEventListener('input', updateAll);
    builderInputsContainer.addEventListener('click', function(event) {
        if (event.target.classList.contains('delete-btn')) {
            event.target.closest('.control-group').remove();
            updateAll();
        }
    });
    formulaInput.addEventListener('input', updateAll);
    resultLabelInput.addEventListener('input', updateAll);

    // --- Core Functions ---
    function updateAll() {
        updatePreview();
        generateHtml(); // Also update the textarea
    }
    
    function addInputField() {
        inputEditorCounter++;
        const div = document.createElement('div');
        div.className = 'control-group';
        const editorId = `label-editor-${inputEditorCounter}`;
        div.innerHTML = `
            <label for="${editorId}">标签:</label>
            <input type="text" id="${editorId}" class="item-label" value="项目 ${builderInputsContainer.children.length + 1}">
            <span class="id-display"></span>
            <button class="delete-btn">删除</button>
        `;
        builderInputsContainer.appendChild(div);
        updateAll();
    }

    function updatePreview() {
        const builderInputs = builderInputsContainer.querySelectorAll('.control-group');
        builderInputs.forEach((group, index) => {
            const inputId = `input${index + 1}`;
            group.querySelector('.id-display').innerHTML = `(ID: <strong>${inputId}</strong>)`;
        });

        previewArea.innerHTML = '';
        builderInputs.forEach((group, index) => {
            const labelText = group.querySelector('.item-label').value;
            const inputId = `input${index + 1}`;
            const itemDiv = document.createElement('div');
            itemDiv.innerHTML = `<label for="preview_${inputId}">${labelText}:</label><input type="number" id="preview_${inputId}" data-id="${inputId}">`;
            previewArea.appendChild(itemDiv);
        });

        previewArea.innerHTML += `<h3>${resultLabelInput.value} <span id="preview_result">0</span></h3>`;
        previewArea.querySelectorAll('input[type="number"]').forEach(input => {
            input.addEventListener('input', calculatePreview);
        });
        calculatePreview();
    }

    function calculatePreview() {
        const formula = formulaInput.value;
        if (!formula) return;
        const scope = {};
        previewArea.querySelectorAll('input[type="number"]').forEach(input => {
            scope[input.dataset.id] = parseFloat(input.value) || 0;
        });
        try {
            const result = new Function(...Object.keys(scope), `return ${formula}`)(...Object.values(scope));
            document.getElementById('preview_result').textContent = result.toLocaleString();
        } catch (e) {
            document.getElementById('preview_result').textContent = '公式错误';
        }
    }

    function generateHtml() {
        const inputsHtml = [];
        builderInputsContainer.querySelectorAll('.control-group').forEach((group, index) => {
            const labelText = group.querySelector('.item-label').value;
            const inputId = `input${index + 1}`;
            inputsHtml.push(`<div class="form-group"><label for="${inputId}">${labelText}:</label><input type="number" id="${inputId}" class="calc-input" value="0"></div>`);
        });

        const finalHtml = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<title>${formNameInput.value || '自定义计算表单'}</title>
<style>body{font-family:sans-serif;max-width:500px;margin:2em auto;padding:1em;border:1px solid #ccc;border-radius:8px;}.form-group{margin-bottom:1em;}label{display:block;margin-bottom:.5em;}input{width:100%;padding:.5em;box-sizing:border-box;}#result-container{margin-top:1.5em;padding-top:1em;border-top:1px solid #eee;font-size:1.2em;font-weight:bold;}</style>
</head>
<body>
<h2>${formNameInput.value || '计算表单'}</h2>
<div id="calc-form">${inputsHtml.join('')}</div>
<div id="result-container"><span>${resultLabelInput.value}</span> <span id="result">0</span></div>
<script>
const inputElements=document.querySelectorAll('.calc-input');
const resultElement=document.getElementById('result');
function calculate(){const scope={};inputElements.forEach(input=>{scope[input.id]=parseFloat(input.value)||0});try{const result=new Function(...Object.keys(scope),\`return ${formulaInput.value}\`)(...Object.values(scope));resultElement.textContent=isNaN(result)||!isFinite(result)?'无效结果':result.toLocaleString()}catch(e){resultElement.textContent='计算错误'}}
inputElements.forEach(input=>input.addEventListener('input',calculate));calculate();
<\/script>
</body>
</html>`;
        generatedHtmlTextarea.value = finalHtml;
        return finalHtml;
    }

    // --- Database Functions ---
    async function saveFormToDB() {
        const formName = formNameInput.value.trim();
        if (!formName) {
            showStatus('请输入保存名称！', 'error');
            return;
        }
        const htmlContent = generateHtml();

        showStatus('正在保存...', 'info');

        try {
            const response = await fetch('save_form.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ name: formName, html: htmlContent })
            });
            const result = await response.json();
            showStatus(result.message, result.success ? 'success' : 'error');
            if (result.success) {
                listFormsFromDB();
            }
        } catch (error) {
            showStatus('请求失败，请检查网络或后台配置。', 'error');
            console.error('Save Error:', error);
        }
    }

    async function listFormsFromDB() {
        showStatus('正在加载列表...', 'info');
        try {
            const response = await fetch('list_forms.php');
            const result = await response.json();
            if (!result.success) {
                showStatus(result.message, 'error');
                return;
            }
            
            savedFormsListDiv.innerHTML = '';
            if (result.data.length === 0) {
                savedFormsListDiv.innerHTML = '<p>数据库中没有已保存的表单。</p>';
            } else {
                const ul = document.createElement('ul');
                result.data.forEach(form => {
                    const li = document.createElement('li');
                    li.textContent = `${form.form_name} (保存于: ${new Date(form.created_at).toLocaleString()})`;
                    li.dataset.id = form.id;
                    li.onclick = () => getFormFromDB(form.id);
                    ul.appendChild(li);
                });
                savedFormsListDiv.appendChild(ul);
            }
            showStatus('列表加载成功！', 'success');

        } catch (error) {
            showStatus('列表加载失败。', 'error');
            console.error('List Error:', error);
        }
    }

    async function getFormFromDB(id) {
        showStatus(`正在加载ID为 ${id} 的表单...`, 'info');
        try {
            const response = await fetch(`get_form.php?id=${id}`);
            const result = await response.json();

            if (result.success) {
                formNameInput.value = result.data.form_name;
                generatedHtmlTextarea.value = result.data.html_content;
                showStatus(`表单“${result.data.form_name}”已加载。`, 'success');
                // 注意：此功能不会将表单设计恢复到上方的设计器中
                // 仅将保存的HTML代码加载到输出文本框
            } else {
                showStatus(result.message, 'error');
            }
        } catch (error) {
            showStatus('加载失败。', 'error');
            console.error('Get Error:', error);
        }
    }

    function showStatus(message, type) {
        statusMessageDiv.textContent = message;
        statusMessageDiv.className = `status-${type}`;
        statusMessageDiv.style.display = 'block';
        setTimeout(() => { statusMessageDiv.style.display = 'none'; }, 4000);
    }

    // --- Initial Load ---
    addInputField();
    listFormsFromDB();

</script>
</body>
</html>