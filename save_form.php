<?php
// save_form.php
require 'db_config.php';
header('Content-Type: application/json');

// フロントエンドから送られてきたJSONデータを取得
$data = json_decode(file_get_contents('php://input'), true);
$formName = isset($data['name']) ? $data['name'] : '';
$htmlContent = isset($data['html']) ? $data['html'] : '';

if (empty($formName) || empty($htmlContent)) {
  echo json_encode(['success' => false, 'message' => 'フォーム名とHTMLコンテンツは必須です。']);
  exit;
}

// SQLインジェクション対策としてプリペアドステートメントを使用
$stmt = $conn->prepare("INSERT INTO saved_forms (form_name, html_content) VALUES (?, ?)");
$stmt->bind_param("ss", $formName, $htmlContent); // "ss" は両方の変数が文字列であることを示す

if ($stmt->execute()) {
  echo json_encode(['success' => true, 'message' => 'フォーム「' . htmlspecialchars($formName) . '」をデータベースに保存しました。']);
} else {
  echo json_encode(['success' => false, 'message' => '保存に失敗しました: ' . $stmt->error]);
}

$stmt->close();
$conn->close();
?>