<?php
// get_form.php
require 'db_config.php';
header('Content-Type: application/json');

// URLからIDを取得 (例: get_form.php?id=5)
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($id <= 0) {
    echo json_encode(['success' => false, 'message' => '無効なIDです。']);
    exit;
}

$stmt = $conn->prepare("SELECT form_name, html_content FROM saved_forms WHERE id = ?");
$stmt->bind_param("i", $id); // "i" は変数が整数であることを示す
$stmt->execute();
$result = $stmt->get_result();

if ($row = $result->fetch_assoc()) {
    echo json_encode(['success' => true, 'data' => $row]);
} else {
    echo json_encode(['success' => false, 'message' => 'フォームが見つかりませんでした。']);
}

$stmt->close();
$conn->close();
?>